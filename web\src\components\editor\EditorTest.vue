<template>
  <div class="editor-test">
    <h2>Editor 智能提示测试</h2>

    <div class="test-info">
      <h3>测试数据</h3>
      <div class="data-section">
        <h4>当前变量 (currentVariables)</h4>
        <pre>{{ JSON.stringify(testCurrentVariables, null, 2) }}</pre>
      </div>

      <div class="data-section">
        <h4>局部变量 (localVariables)</h4>
        <pre>{{ JSON.stringify(testLocalVariables, null, 2) }}</pre>
      </div>

      <div class="data-section">
        <h4>全局变量 (globalVariables)</h4>
        <pre>{{ JSON.stringify(testGlobalVariables, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-instructions">
      <h3>测试说明</h3>
      <div class="alert">
        <strong>修复说明：</strong> 已修复智能提示逐级展开问题，现在应该按层级显示建议而不是展平所有路径。
      </div>
      <ol>
        <li><strong>自定义变量：</strong>输入字母，应该看到 userName, userAge 等当前变量</li>
        <li>
          <strong>第一级：</strong>输入 "_data." 应该<u>只</u>看到 orderInfo, productList, systemConfig（不是完整路径）
        </li>
        <li><strong>第二级：</strong>输入 "_data.orderInfo." 应该<u>只</u>看到 orderId, customer</li>
        <li><strong>第三级：</strong>输入 "_data.orderInfo.customer." 应该<u>只</u>看到 name, phone</li>
        <li><strong>JavaScript 默认：</strong>输入 "JSON." 应该看到 parse, stringify 等 JavaScript 默认方法</li>
        <li><strong>字符串方法：</strong>输入 '"hello".' 应该看到 charAt, indexOf 等字符串方法</li>
        <li><strong>数组方法：</strong>输入 "[1,2,3]." 应该看到 push, pop, map 等数组方法</li>
        <li><strong>手动触发：</strong>按 Ctrl+Space 可以手动触发智能提示</li>
        <li><strong>调试：</strong>打开浏览器控制台查看调试信息</li>
      </ol>
    </div>

    <div class="editor-container">
      <h3>编辑器测试区域</h3>
      <editor
        v-model:value="testCode"
        language="typescript"
        :enable-intellisense="true"
        :current-variables="testCurrentVariables"
        :local-variables="testLocalVariables"
        :global-variables="testGlobalVariables"
        :functions="testFunctions"
        style="height: 300px; border: 1px solid #ccc"
      />
    </div>

    <div class="test-output">
      <h3>编辑器内容</h3>
      <pre>{{ testCode }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Editor from './index.vue';
// 确保加载智能提示配置
import './loader';

const testCode = ref(`// 在这里测试智能提示
// 测试自定义变量：输入 _data. 或 userName
// 测试 JavaScript 默认：输入 JSON. 或 "hello".
// 按 Ctrl+Space 手动触发智能提示

`);

// 测试用的当前变量
const testCurrentVariables = ref([
  {
    id: 'userName',
    key: 'userName',
    description: '用户名',
    type: 'string',
    children: [],
  },
  {
    id: 'userAge',
    key: 'userAge',
    description: '用户年龄',
    type: 'number',
    children: [],
  },
]);

// 测试用的局部变量（带嵌套结构）
const testLocalVariables = ref([
  {
    id: 'orderInfo',
    key: 'orderInfo',
    description: '订单信息',
    type: 'object',
    children: [
      {
        id: 'orderId',
        key: 'orderId',
        description: '订单ID',
        type: 'string',
        children: [],
      },
      {
        id: 'customer',
        key: 'customer',
        description: '客户信息',
        type: 'object',
        children: [
          {
            id: 'name',
            key: 'name',
            description: '客户姓名',
            type: 'string',
            children: [],
          },
          {
            id: 'phone',
            key: 'phone',
            description: '客户电话',
            type: 'string',
            children: [],
          },
        ],
      },
    ],
  },
  {
    id: 'productList',
    key: 'productList',
    description: '产品列表',
    type: 'array',
    children: [
      {
        id: 'productId',
        key: 'productId',
        description: '产品ID',
        type: 'string',
        children: [],
      },
      {
        id: 'productName',
        key: 'productName',
        description: '产品名称',
        type: 'string',
        children: [],
      },
    ],
  },
]);

// 测试用的全局变量
const testGlobalVariables = ref([
  {
    id: 'systemConfig',
    key: 'systemConfig',
    description: '系统配置',
    type: 'object',
    children: [
      {
        id: 'database',
        key: 'database',
        description: '数据库配置',
        type: 'object',
        children: [
          {
            id: 'host',
            key: 'host',
            description: '数据库主机',
            type: 'string',
            children: [],
          },
          {
            id: 'port',
            key: 'port',
            description: '数据库端口',
            type: 'number',
            children: [],
          },
        ],
      },
    ],
  },
]);

// 测试用的函数列表
const testFunctions = ref([
  {
    value: 'formatDate',
    label: 'formatDate',
    script: 'Utils.formatDate()',
    remark: '格式化日期',
  },
  {
    value: 'generateId',
    label: 'generateId',
    script: 'Utils.generateId()',
    remark: '生成唯一ID',
  },
]);
</script>

<style scoped>
.editor-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-info {
  margin-bottom: 20px;
}

.data-section {
  margin-bottom: 15px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.data-section h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.data-section pre {
  margin: 0;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
  background: white;
  padding: 10px;
  border-radius: 4px;
}

.test-instructions {
  margin-bottom: 20px;
  padding: 15px;
  background: #e3f2fd;
  border-radius: 4px;
}

.test-instructions h3 {
  margin-top: 0;
}

.alert {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.editor-container {
  margin-bottom: 20px;
}

.test-output {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
}

.test-output pre {
  margin: 0;
  font-size: 14px;
}
</style>
