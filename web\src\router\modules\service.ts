import {
  ApiIcon,
  ArrowLeftRight1Icon,
  CloudIcon,
  SystemApplicationIcon,
  SystemComponentsIcon,
  TimeIcon,
  ActivityIcon,
  Functions1Icon,
} from 'tdesign-icons-vue-next';
import { shallowRef } from 'vue';

import Layout from '@/layouts/index.vue';

export default [
  {
    path: '/service',
    component: Layout,
    redirect: '/service/base',
    name: 'Service',
    meta: {
      title: {
        zh_CN: '服务',
        en_US: 'Service',
      },
      icon: shallowRef(SystemComponentsIcon),
      orderNo: 2,
    },
    children: [
      {
        path: 'base',
        name: 'ServiceBase',
        component: () => import('@/pages/service/base/index.vue'),
        meta: {
          title: {
            zh_CN: '概览',
            en_US: 'Overview',
          },
          icon: shallowRef(ActivityIcon),
        },
      },
      // {
      //   path: 'dataSource',
      //   name: 'DataSource',
      //   component: () => import('@/pages/service/dataSource/index.vue'),
      //   meta: {
      //     title: {
      //       zh_CN: '数据源',
      //       en_US: 'Data Source',
      //     },
      //   },
      // },
      // {
      //   path: 'permissions',
      //   name: 'ThirdPartyPermissions',
      //   component: () => import('@/pages/service/permissions/index.vue'),
      //   meta: {
      //     title: {
      //       zh_CN: '第三方权限',
      //       en_US: 'Third Party Permissions',
      //     },
      //   },
      // },
      {
        path: 'variables',
        name: 'GlobalVariables',
        component: () => import('@/pages/service/variables/index.vue'),
        meta: {
          title: {
            zh_CN: '全局变量',
            en_US: 'Global Variables',
          },
          icon: shallowRef(SystemApplicationIcon),
        },
      },
      // {
      //   path: 'gateway',
      //   name: 'Gateway',
      //   component: () => import('@/pages/service/apis/Gateway.vue'),
      //   meta: {
      //     title: {
      //       zh_CN: '网关',
      //       en_US: 'Gateway',
      //     },
      //   },
      // },
      {
        path: 'thirdPartyApi',
        name: 'ThirdPartyApi',
        component: () => import('@/pages/service/apis/ThirdPartyApi.vue'),
        meta: {
          title: {
            zh_CN: '第三方API',
            en_US: 'Third Party API',
          },
          icon: shallowRef(CloudIcon),
        },
      },
      {
        path: 'systemApi',
        name: 'SystemApi',
        component: () => import('@/pages/service/apis/SystemApi.vue'),
        meta: {
          title: {
            zh_CN: '系统API',
            en_US: 'System API',
          },
          icon: shallowRef(ApiIcon),
        },
      },
      {
        path: 'jobs',
        name: 'CronJob',
        component: () => import('@/pages/service/jobs/index.vue'),
        meta: {
          title: {
            zh_CN: '定时任务',
            en_US: 'Cron Job',
          },
          icon: shallowRef(TimeIcon),
        },
      },
      {
        path: 'events',
        name: 'Events',
        component: () => import('@/pages/service/events/index.vue'),
        meta: {
          title: {
            zh_CN: '事件',
            en_US: 'Events',
          },
          icon: shallowRef(ArrowLeftRight1Icon),
        },
      },
      {
        path: 'action-library',
        name: 'ActionLibrary',
        component: () => import('@/pages/service/action-library/ActionLibraryManagement.vue'),
        meta: {
          title: {
            zh_CN: '动作库',
            en_US: 'Action Library',
          },
          icon: shallowRef(Functions1Icon),
        },
      },
      {
        path: 'editor-test',
        name: 'EditorTest',
        component: () => import('@/components/editor/EditorTest.vue'),
        meta: {
          title: {
            zh_CN: 'Editor测试',
            en_US: 'Editor Test',
          },
          icon: shallowRef(Functions1Icon),
        },
      },
    ],
  },
];
