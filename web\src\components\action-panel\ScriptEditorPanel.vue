<template>
  <t-space direction="vertical" style="width: 100%">
    <editor
      ref="editorRef"
      v-model:value="scriptValue"
      language="typescript"
      :enable-intellisense="true"
      :current-variables="actionFlowStore.currentVariables"
      :local-variables="actionFlowStore.localVariables"
      :global-variables="globalVariables"
      :functions="functionList"
      style="height: 250px"
    ></editor>
    <t-tabs default-value="3">
      <t-tab-panel value="1" label="局部变量">
        <variable-tree
          :filter-text="searchText"
          :variable-list="actionFlowStore.localVariables"
          @dblclick="onDblclickVariable"
        ></variable-tree>
      </t-tab-panel>
      <t-tab-panel value="2" label="全局变量">
        <variable-tree
          :filter-text="searchText"
          :variable-list="globalVariables"
          @dblclick="onDblclickVariable"
        ></variable-tree>
      </t-tab-panel>
      <t-tab-panel value="3" label="函数">
        <enhanced-function-list @dblclick="onDblclickFunction"></enhanced-function-list>
      </t-tab-panel>
      <template #action>
        <t-input v-model="searchText" class="search-input" size="small" placeholder="搜索" clearable>
          <template #prefixIcon>
            <search-icon></search-icon>
          </template>
        </t-input>
      </template>
    </t-tabs>
  </t-space>
</template>
<script lang="ts">
export default {
  name: 'ScriptEditorPanel',
};
</script>
<script setup lang="ts">
import { debounce } from 'lodash-es';
import { SearchIcon } from 'tdesign-icons-vue-next';
import { onActivated, onMounted, ref, watch, computed } from 'vue';

import { useActionFlowStore } from '@/components/action-panel/store/index';
import Editor from '@/components/editor/index.vue';
// 导入编辑器加载器以初始化智能提示
import '@/components/editor/loader';

import EnhancedFunctionList from './EnhancedFunctionList.vue';
import { FlowData } from './model';
import { getGlobalVariables } from './utils';
import VariableTree from './VariableTree.vue';

const globalVariables = ref<FlowData[]>([]);
onActivated(async () => {
  globalVariables.value = await getGlobalVariables();
});
onMounted(async () => {
  globalVariables.value = await getGlobalVariables();
});

// 函数列表数据，用于智能提示
const functionList = computed(() => {
  const functions: any[] = [];

  // 基础函数数据
  const functionData = [
    {
      value: 'Common',
      label: '常用函数',
      children: [
        {
          value: 'UUID',
          label: '随机ID',
          script: 'Utils.UUID()',
          remark: '生成随机的UUID',
        },
        {
          value: 'NOW',
          label: '当前时间',
          script: 'Utils.NOW()',
          remark: '获取当前时间',
        },
        {
          value: 'DATE_TO_STRING',
          label: '时间转换文本',
          script: 'Utils.DATE_TO_STRING(time, "yyyy-MM-dd HH:mm:ss")',
          remark: '将时间转换为文本',
        },
        {
          value: 'JSON_PARSE',
          label: 'JSON解析',
          script: 'Utils.JSON_PARSE(jsonString)',
          remark: '解析JSON字符串为对象',
        },
      ],
    },
    {
      value: 'String',
      label: '字符串函数',
      children: [
        {
          value: 'STRING_CONCAT',
          label: '字符串拼接',
          script: 'Utils.STRING_CONCAT(str1, str2, ...)',
          remark: '拼接多个字符串',
        },
        {
          value: 'STRING_LENGTH',
          label: '字符串长度',
          script: 'Utils.STRING_LENGTH(input)',
          remark: '获取字符串长度',
        },
      ],
    },
  ];

  // 从函数数据中提取所有函数
  functionData.forEach((category) => {
    if (category.children) {
      category.children.forEach((func: any) => {
        functions.push({
          value: func.value,
          label: func.label,
          script: func.script,
          remark: func.remark,
        });
      });
    }
  });

  return functions;
});

const props = defineProps<{
  script: string;
}>();

const editorRef = ref();
const searchText = ref('');
const scriptValue = ref<string>(props.script);
const actionFlowStore = useActionFlowStore();

const onDblclickVariable = ({ path, item }) => {
  const prefix = '_data.';
  editorRef.value.insertText(prefix + path);
};

const onDblclickFunction = (func) => {
  editorRef.value.insertText(func.script);
};

const emits = defineEmits(['update:script']);
watch(
  () => scriptValue.value,
  debounce((newValue) => {
    emits('update:script', newValue);
  }, 500),
);
</script>
<style lang="less" scoped>
.search-input {
  position: relative;
  top: 8px;
  width: 180px;
}
</style>
