import * as monaco from 'monaco-editor';

// 变量和函数数据接口
interface VariableData {
  id: string;
  key: string;
  path?: string;
  pathDescription?: string;
  description?: string;
  type: string;
  children?: VariableData[];
}

interface FunctionData {
  value: string;
  label: string;
  script: string;
  remark: string;
}

// 全局变量存储
let currentVariables: VariableData[] = [];
let localVariables: VariableData[] = [];
let globalVariables: VariableData[] = [];
let functions: FunctionData[] = [];

// 更新智能提示数据的函数
export const updateIntelliSenseData = (data: {
  currentVariables?: VariableData[];
  localVariables?: VariableData[];
  globalVariables?: VariableData[];
  functions?: FunctionData[];
}) => {
  if (data.currentVariables) {
    currentVariables = data.currentVariables;
    console.log('更新当前变量:', currentVariables);
  }
  if (data.localVariables) {
    localVariables = data.localVariables;
    console.log('更新局部变量:', localVariables);
  }
  if (data.globalVariables) {
    globalVariables = data.globalVariables;
    console.log('更新全局变量:', globalVariables);
  }
  if (data.functions) {
    functions = data.functions;
    console.log('更新函数列表:', functions);
  }
};

// 扁平化变量列表，生成所有可能的路径
const flattenVariables = (
  variables: VariableData[],
  prefix: string = '',
): Array<{ path: string; variable: VariableData }> => {
  const result: Array<{ path: string; variable: VariableData }> = [];

  variables.forEach((variable) => {
    const currentPath = prefix ? `${prefix}.${variable.key}` : variable.key;
    result.push({ path: currentPath, variable });

    if (variable.children && variable.children.length > 0) {
      result.push(...flattenVariables(variable.children, currentPath));
    }
  });

  return result;
};

// 获取变量建议
const getVariableSuggestions = (variables: VariableData[], prefix: string = ''): any[] => {
  const flattened = flattenVariables(variables);

  return flattened.map(({ path, variable }) => {
    const displayPath = prefix ? `${prefix}${path}` : path;
    return {
      label: displayPath,
      kind: monaco.languages.CompletionItemKind.Variable,
      insertText: displayPath,
      detail: variable.type,
      documentation: variable.description || variable.pathDescription || `${variable.type} 类型变量`,
      sortText: `0_${displayPath}`,
    };
  });
};

// 获取函数建议
const getFunctionSuggestions = (): any[] => {
  return functions.map((func) => ({
    label: func.label,
    kind: monaco.languages.CompletionItemKind.Function,
    insertText: func.script,
    detail: func.label,
    documentation: func.remark,
    sortText: `1_${func.label}`,
  }));
};

// 获取内置对象和方法的建议
const getBuiltinSuggestions = (): any[] => {
  return [
    {
      label: 'Utils',
      kind: monaco.languages.CompletionItemKind.Module,
      insertText: 'Utils.',
      detail: '工具类',
      documentation: '包含各种实用函数的工具类',
      sortText: '2_Utils',
    },
    {
      label: '_data',
      kind: monaco.languages.CompletionItemKind.Variable,
      insertText: '_data.',
      detail: '数据对象',
      documentation: '包含局部变量和全局变量的数据对象',
      sortText: '0__data',
    },
    {
      label: 'console.log',
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: 'console.log($1)',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '控制台输出',
      documentation: '在控制台输出信息',
      sortText: '3_console',
    },
    {
      label: 'JSON.parse',
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: 'JSON.parse($1)',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'JSON解析',
      documentation: '解析JSON字符串',
      sortText: '3_JSON',
    },
    {
      label: 'JSON.stringify',
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: 'JSON.stringify($1)',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'JSON序列化',
      documentation: '将对象序列化为JSON字符串',
      sortText: '3_JSON',
    },
  ];
};

// 查找嵌套变量的子属性
const findNestedVariable = (variables: VariableData[], path: string): VariableData | null => {
  const pathParts = path.split('.');
  console.log('查找嵌套变量:', { path, pathParts, variablesCount: variables.length });

  for (const variable of variables) {
    console.log('检查变量:', { variableKey: variable.key, targetKey: pathParts[0], hasChildren: !!variable.children });
    if (variable.key === pathParts[0]) {
      if (pathParts.length === 1) {
        console.log('找到目标变量:', variable);
        return variable;
      }
      // 递归查找子属性
      if (variable.children && variable.children.length > 0) {
        const remainingPath = pathParts.slice(1).join('.');
        console.log('递归查找子属性:', { remainingPath, childrenCount: variable.children.length });
        return findNestedVariable(variable.children, remainingPath);
      }
    }
  }
  console.log('未找到嵌套变量');
  return null;
};

// 获取嵌套对象的子属性建议
const getNestedSuggestions = (variables: VariableData[], path: string): any[] => {
  console.log('获取嵌套建议:', { path, variablesCount: variables.length });
  const variable = findNestedVariable(variables, path);
  if (variable && variable.children && variable.children.length > 0) {
    console.log('找到嵌套变量，生成子属性建议:', variable.children.length);
    return variable.children.map((child) => ({
      label: child.key,
      kind: monaco.languages.CompletionItemKind.Property,
      insertText: child.key,
      detail: child.type,
      documentation: child.description || child.pathDescription || `${child.type} 类型属性`,
      sortText: `0_${child.key}`,
    }));
  }
  console.log('未找到嵌套变量或无子属性');
  return [];
};

// 注册TypeScript/JavaScript智能提示提供者
monaco.languages.registerCompletionItemProvider('typescript', {
  triggerCharacters: ['.', '_'],
  provideCompletionItems: (model, position) => {
    const suggestions: any[] = [];
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    // 简化的输入分析 - 检查是否以特定模式结尾
    const trimmedText = textBeforePointer.trim();

    console.log('智能提示触发:', {
      textBeforePointer,
      trimmedText,
      currentVariables: currentVariables.length,
      localVariables: localVariables.length,
      globalVariables: globalVariables.length,
      functions: functions.length,
    });

    // 检查是否是嵌套对象访问（如 _data.someObject.）
    const dataNestedMatch = trimmedText.match(/_data\.([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);
    console.log('嵌套匹配检查:', { dataNestedMatch, trimmedText });

    if (dataNestedMatch) {
      const nestedPath = dataNestedMatch[1];
      console.log('找到嵌套路径:', nestedPath);

      // 在局部变量中查找
      const localNestedSuggestions = getNestedSuggestions(localVariables, nestedPath);
      suggestions.push(...localNestedSuggestions);

      // 在全局变量中查找
      const globalNestedSuggestions = getNestedSuggestions(globalVariables, nestedPath);
      suggestions.push(...globalNestedSuggestions);

      console.log('嵌套建议数量:', suggestions.length);
    }
    // 检查是否是当前变量的嵌套访问（如 someVar.someProperty.）
    else {
      const currentNestedMatch = trimmedText.match(/([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);
      console.log('当前变量嵌套匹配检查:', { currentNestedMatch, trimmedText });

      if (currentNestedMatch) {
        const nestedPath = currentNestedMatch[1];
        console.log('找到当前变量嵌套路径:', nestedPath);
        const currentNestedSuggestions = getNestedSuggestions(currentVariables, nestedPath);
        suggestions.push(...currentNestedSuggestions);
        console.log('当前变量嵌套建议数量:', currentNestedSuggestions.length);
      }
    }

    // 如果没有找到嵌套建议，使用原有逻辑
    if (suggestions.length === 0) {
      // 如果是在 _data. 后面，提供局部和全局变量建议
      if (trimmedText.endsWith('_data.')) {
        // 添加局部变量建议
        const localSuggestions = getVariableSuggestions(localVariables, '_data.');
        suggestions.push(...localSuggestions);

        // 添加全局变量建议
        const globalSuggestions = getVariableSuggestions(globalVariables, '_data.');
        suggestions.push(...globalSuggestions);
      }
      // 如果是在 Utils. 后面，提供函数建议
      else if (trimmedText.endsWith('Utils.')) {
        const functionSuggestions = getFunctionSuggestions();
        suggestions.push(...functionSuggestions);
      }
      // 否则提供所有类型的建议
      else {
        // 添加临时变量建议（不需要 _data 前缀）
        const currentSuggestions = getVariableSuggestions(currentVariables);
        suggestions.push(...currentSuggestions);

        // 添加内置对象建议
        const builtinSuggestions = getBuiltinSuggestions();
        suggestions.push(...builtinSuggestions);

        // 添加函数建议
        const functionSuggestions = getFunctionSuggestions();
        suggestions.push(...functionSuggestions);
      }
    }

    return {
      suggestions,
    };
  },
});

// 同样为JavaScript语言注册
monaco.languages.registerCompletionItemProvider('javascript', {
  triggerCharacters: ['.', '_'],
  provideCompletionItems: (model, position) => {
    const suggestions: any[] = [];
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    // 简化的输入分析 - 检查是否以特定模式结尾
    const trimmedText = textBeforePointer.trim();

    // 检查是否是嵌套对象访问（如 _data.someObject.）
    const dataNestedMatch = trimmedText.match(/_data\.([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);
    if (dataNestedMatch) {
      const nestedPath = dataNestedMatch[1];
      // 在局部变量中查找
      const localNestedSuggestions = getNestedSuggestions(localVariables, nestedPath);
      suggestions.push(...localNestedSuggestions);

      // 在全局变量中查找
      const globalNestedSuggestions = getNestedSuggestions(globalVariables, nestedPath);
      suggestions.push(...globalNestedSuggestions);
    }
    // 检查是否是当前变量的嵌套访问（如 someVar.someProperty.）
    else {
      const currentNestedMatch = trimmedText.match(/([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);
      if (currentNestedMatch) {
        const nestedPath = currentNestedMatch[1];
        const currentNestedSuggestions = getNestedSuggestions(currentVariables, nestedPath);
        suggestions.push(...currentNestedSuggestions);
      }
    }

    // 如果没有找到嵌套建议，使用原有逻辑
    if (suggestions.length === 0) {
      // 如果是在 _data. 后面，提供局部和全局变量建议
      if (trimmedText.endsWith('_data.')) {
        // 添加局部变量建议
        const localSuggestions = getVariableSuggestions(localVariables, '_data.');
        suggestions.push(...localSuggestions);

        // 添加全局变量建议
        const globalSuggestions = getVariableSuggestions(globalVariables, '_data.');
        suggestions.push(...globalSuggestions);
      }
      // 如果是在 Utils. 后面，提供函数建议
      else if (trimmedText.endsWith('Utils.')) {
        const functionSuggestions = getFunctionSuggestions();
        suggestions.push(...functionSuggestions);
      }
      // 否则提供所有类型的建议
      else {
        // 添加临时变量建议（不需要 _data 前缀）
        const currentSuggestions = getVariableSuggestions(currentVariables);
        suggestions.push(...currentSuggestions);

        // 添加内置对象建议
        const builtinSuggestions = getBuiltinSuggestions();
        suggestions.push(...builtinSuggestions);

        // 添加函数建议
        const functionSuggestions = getFunctionSuggestions();
        suggestions.push(...functionSuggestions);
      }
    }

    return {
      suggestions,
    };
  },
});
