import * as monaco from 'monaco-editor';

// 变量和函数数据接口
interface VariableData {
  id: string;
  key: string;
  path?: string;
  pathDescription?: string;
  description?: string;
  type: string;
  children?: VariableData[];
}

interface FunctionData {
  value: string;
  label: string;
  script: string;
  remark: string;
}

// 全局变量存储
let currentVariables: VariableData[] = [];
let localVariables: VariableData[] = [];
let globalVariables: VariableData[] = [];
let functions: FunctionData[] = [];

// 更新智能提示数据的函数
export const updateIntelliSenseData = (data: {
  currentVariables?: VariableData[];
  localVariables?: VariableData[];
  globalVariables?: VariableData[];
  functions?: FunctionData[];
}) => {
  if (data.currentVariables) {
    currentVariables = data.currentVariables;
    console.log('更新当前变量:', currentVariables);
  }
  if (data.localVariables) {
    localVariables = data.localVariables;
    console.log('更新局部变量:', localVariables);
  }
  if (data.globalVariables) {
    globalVariables = data.globalVariables;
    console.log('更新全局变量:', globalVariables);
  }
  if (data.functions) {
    functions = data.functions;
    console.log('更新函数列表:', functions);
  }
};

// 扁平化变量列表，生成所有可能的路径
const flattenVariables = (
  variables: VariableData[],
  prefix: string = '',
): Array<{ path: string; variable: VariableData }> => {
  const result: Array<{ path: string; variable: VariableData }> = [];

  variables.forEach((variable) => {
    const currentPath = prefix ? `${prefix}.${variable.key}` : variable.key;
    result.push({ path: currentPath, variable });

    if (variable.children && variable.children.length > 0) {
      result.push(...flattenVariables(variable.children, currentPath));
    }
  });

  return result;
};

// 获取变量建议（展平所有层级）
const getVariableSuggestions = (variables: VariableData[], prefix: string = ''): any[] => {
  const flattened = flattenVariables(variables);

  return flattened.map(({ path, variable }) => {
    const displayPath = prefix ? `${prefix}${path}` : path;
    return {
      label: displayPath,
      kind: monaco.languages.CompletionItemKind.Variable,
      insertText: displayPath,
      detail: variable.type,
      documentation: variable.description || variable.pathDescription || `${variable.type} 类型变量`,
      sortText: `0_${displayPath}`,
    };
  });
};

// 获取第一级变量建议（只显示第一级，不展平）
const getFirstLevelSuggestions = (variables: VariableData[]): any[] => {
  return variables.map((variable) => ({
    label: variable.key,
    kind:
      variable.children && variable.children.length > 0
        ? monaco.languages.CompletionItemKind.Module
        : monaco.languages.CompletionItemKind.Variable,
    insertText: variable.key,
    detail: variable.type,
    documentation: variable.description || variable.pathDescription || `${variable.type} 类型变量`,
    sortText: `0_${variable.key}`,
  }));
};

// 获取函数建议
const getFunctionSuggestions = (): any[] => {
  return functions.map((func) => ({
    label: func.label,
    kind: monaco.languages.CompletionItemKind.Function,
    insertText: func.script,
    detail: func.label,
    documentation: func.remark,
    sortText: `1_${func.label}`,
  }));
};

// 获取内置对象和方法的建议
const getBuiltinSuggestions = (): any[] => {
  return [
    {
      label: 'Utils',
      kind: monaco.languages.CompletionItemKind.Module,
      insertText: 'Utils.',
      detail: '工具类',
      documentation: '包含各种实用函数的工具类',
      sortText: '2_Utils',
    },
    {
      label: '_data',
      kind: monaco.languages.CompletionItemKind.Variable,
      insertText: '_data.',
      detail: '数据对象',
      documentation: '包含局部变量和全局变量的数据对象',
      sortText: '0__data',
    },
    {
      label: 'console.log',
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: 'console.log($1)',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '控制台输出',
      documentation: '在控制台输出信息',
      sortText: '3_console',
    },
    {
      label: 'JSON.parse',
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: 'JSON.parse($1)',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'JSON解析',
      documentation: '解析JSON字符串',
      sortText: '3_JSON',
    },
    {
      label: 'JSON.stringify',
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: 'JSON.stringify($1)',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'JSON序列化',
      documentation: '将对象序列化为JSON字符串',
      sortText: '3_JSON',
    },
  ];
};

// 查找嵌套变量的子属性
const findNestedVariable = (variables: VariableData[], path: string): VariableData | null => {
  const pathParts = path.split('.');
  console.log('查找嵌套变量:', { path, pathParts });

  for (const variable of variables) {
    if (variable.key === pathParts[0]) {
      if (pathParts.length === 1) {
        console.log('找到目标变量:', variable.key, '子属性数量:', variable.children?.length || 0);
        return variable;
      }
      // 递归查找子属性
      if (variable.children && variable.children.length > 0) {
        const remainingPath = pathParts.slice(1).join('.');
        return findNestedVariable(variable.children, remainingPath);
      }
    }
  }
  return null;
};

// 获取嵌套对象的子属性建议
const getNestedSuggestions = (variables: VariableData[], path: string): any[] => {
  const variable = findNestedVariable(variables, path);
  if (variable && variable.children && variable.children.length > 0) {
    console.log(
      '生成子属性建议:',
      variable.children.map((c) => c.key),
    );
    return variable.children.map((child) => ({
      label: child.key,
      kind:
        child.children && child.children.length > 0
          ? monaco.languages.CompletionItemKind.Module
          : monaco.languages.CompletionItemKind.Property,
      insertText: child.key,
      detail: child.type,
      documentation: child.description || child.pathDescription || `${child.type} 类型属性`,
      sortText: `0_${child.key}`,
    }));
  }
  return [];
};

// 检查是否应该提供自定义建议
const shouldProvideCustomSuggestions = (trimmedText: string, triggerCharacter?: string): boolean => {
  // 如果是通过点号触发的，检查是否是自定义对象
  if (triggerCharacter === '.') {
    // 如果是 _data. 相关的访问，提供自定义建议
    if (trimmedText.includes('_data.') || trimmedText.endsWith('_data.')) {
      return true;
    }
    // 如果是 Utils. 相关的访问，提供自定义建议
    if (trimmedText.includes('Utils.') || trimmedText.endsWith('Utils.')) {
      return true;
    }
    // 如果是我们自定义变量的访问（带点号），提供自定义建议
    const customVariableMatch = trimmedText.match(/([a-zA-Z_$][a-zA-Z0-9_$]*)\./);
    if (customVariableMatch) {
      const varName = customVariableMatch[1];
      // 检查是否是我们的自定义变量
      const isCustomVariable =
        currentVariables.some((v) => v.key === varName) ||
        localVariables.some((v) => v.key === varName) ||
        globalVariables.some((v) => v.key === varName);
      if (isCustomVariable) {
        return true;
      }
    }
    return false; // 其他点号触发的情况让默认智能提示处理
  }

  // 如果不是点号触发，总是提供自定义建议（包括变量名提示）
  return true;
};

// 注册TypeScript/JavaScript智能提示提供者
monaco.languages.registerCompletionItemProvider('typescript', {
  triggerCharacters: ['.', '_'],
  provideCompletionItems: async (model, position, context) => {
    const suggestions: any[] = [];
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    // 简化的输入分析 - 检查是否以特定模式结尾
    const trimmedText = textBeforePointer.trim();

    // 获取触发字符
    const triggerCharacter = context.triggerCharacter;

    console.log('智能提示触发:', {
      trimmedText,
      triggerCharacter,
      shouldProvideCustom: shouldProvideCustomSuggestions(trimmedText, triggerCharacter),
    });

    // 只在特定情况下提供自定义建议
    if (shouldProvideCustomSuggestions(trimmedText, triggerCharacter)) {
      // 检查是否是嵌套对象访问（如 _data.someObject.）
      const dataNestedMatch = trimmedText.match(/_data\.([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);

      if (dataNestedMatch) {
        const nestedPath = dataNestedMatch[1];
        console.log('_data 嵌套路径:', nestedPath);

        // 在局部变量中查找
        const localNestedSuggestions = getNestedSuggestions(localVariables, nestedPath);
        suggestions.push(...localNestedSuggestions);

        // 在全局变量中查找
        const globalNestedSuggestions = getNestedSuggestions(globalVariables, nestedPath);
        suggestions.push(...globalNestedSuggestions);
      }
      // 检查是否是当前变量的嵌套访问（如 someVar.someProperty.）
      else {
        const currentNestedMatch = trimmedText.match(/([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);

        if (currentNestedMatch) {
          const nestedPath = currentNestedMatch[1];
          console.log('当前变量嵌套路径:', nestedPath);
          const currentNestedSuggestions = getNestedSuggestions(currentVariables, nestedPath);
          suggestions.push(...currentNestedSuggestions);
        }
      }

      // 如果没有找到嵌套建议，使用原有逻辑
      if (suggestions.length === 0) {
        // 如果是在 _data. 后面，提供局部和全局变量建议（只显示第一级）
        if (trimmedText.endsWith('_data.')) {
          // 添加局部变量建议（只显示第一级）
          const localSuggestions = getFirstLevelSuggestions(localVariables);
          suggestions.push(...localSuggestions);

          // 添加全局变量建议（只显示第一级）
          const globalSuggestions = getFirstLevelSuggestions(globalVariables);
          suggestions.push(...globalSuggestions);
        }
        // 如果是在 Utils. 后面，提供函数建议
        else if (trimmedText.endsWith('Utils.')) {
          const functionSuggestions = getFunctionSuggestions();
          suggestions.push(...functionSuggestions);
        }
        // 否则提供自定义变量建议
        else {
          // 添加临时变量建议（不需要 _data 前缀）
          const currentSuggestions = getFirstLevelSuggestions(currentVariables);
          suggestions.push(...currentSuggestions);

          // 添加内置对象建议
          const builtinSuggestions = getBuiltinSuggestions();
          suggestions.push(...builtinSuggestions);

          // 添加函数建议
          const functionSuggestions = getFunctionSuggestions();
          suggestions.push(...functionSuggestions);
        }
      }

      return {
        suggestions,
      };
    }

    // 对于其他情况，返回空建议，让默认的 TypeScript 智能提示处理
    return {
      suggestions: [],
    };
  },
});

// 同样为JavaScript语言注册
monaco.languages.registerCompletionItemProvider('javascript', {
  triggerCharacters: ['.', '_'],
  provideCompletionItems: async (model, position) => {
    const suggestions: any[] = [];
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    // 简化的输入分析 - 检查是否以特定模式结尾
    const trimmedText = textBeforePointer.trim();

    // 只在特定情况下提供自定义建议
    if (shouldProvideCustomSuggestions(trimmedText)) {
      // 检查是否是嵌套对象访问（如 _data.someObject.）
      const dataNestedMatch = trimmedText.match(/_data\.([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);
      if (dataNestedMatch) {
        const nestedPath = dataNestedMatch[1];
        // 在局部变量中查找
        const localNestedSuggestions = getNestedSuggestions(localVariables, nestedPath);
        suggestions.push(...localNestedSuggestions);

        // 在全局变量中查找
        const globalNestedSuggestions = getNestedSuggestions(globalVariables, nestedPath);
        suggestions.push(...globalNestedSuggestions);
      }
      // 检查是否是当前变量的嵌套访问（如 someVar.someProperty.）
      else {
        const currentNestedMatch = trimmedText.match(/([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);
        if (currentNestedMatch) {
          const nestedPath = currentNestedMatch[1];
          const currentNestedSuggestions = getNestedSuggestions(currentVariables, nestedPath);
          suggestions.push(...currentNestedSuggestions);
        }
      }

      // 如果没有找到嵌套建议，使用原有逻辑
      if (suggestions.length === 0) {
        // 如果是在 _data. 后面，提供局部和全局变量建议（只显示第一级）
        if (trimmedText.endsWith('_data.')) {
          // 添加局部变量建议（只显示第一级）
          const localSuggestions = getFirstLevelSuggestions(localVariables);
          suggestions.push(...localSuggestions);

          // 添加全局变量建议（只显示第一级）
          const globalSuggestions = getFirstLevelSuggestions(globalVariables);
          suggestions.push(...globalSuggestions);
        }
        // 如果是在 Utils. 后面，提供函数建议
        else if (trimmedText.endsWith('Utils.')) {
          const functionSuggestions = getFunctionSuggestions();
          suggestions.push(...functionSuggestions);
        }
        // 否则提供自定义变量建议
        else {
          // 添加临时变量建议（不需要 _data 前缀）
          const currentSuggestions = getFirstLevelSuggestions(currentVariables);
          suggestions.push(...currentSuggestions);

          // 添加内置对象建议
          const builtinSuggestions = getBuiltinSuggestions();
          suggestions.push(...builtinSuggestions);

          // 添加函数建议
          const functionSuggestions = getFunctionSuggestions();
          suggestions.push(...functionSuggestions);
        }
      }

      return {
        suggestions,
      };
    }

    // 对于其他情况，返回空建议，让默认的 JavaScript 智能提示处理
    return {
      suggestions: [],
    };
  },
});
