import * as monaco from 'monaco-editor';

// 变量和函数数据接口
interface VariableData {
  id: string;
  key: string;
  path?: string;
  pathDescription?: string;
  description?: string;
  type: string;
  children?: VariableData[];
}

interface FunctionData {
  value: string;
  label: string;
  script: string;
  remark: string;
}

// 全局变量存储
let currentVariables: VariableData[] = [];
let localVariables: VariableData[] = [];
let globalVariables: VariableData[] = [];
let functions: FunctionData[] = [];

// 更新智能提示数据的函数
export const updateIntelliSenseData = (data: {
  currentVariables?: VariableData[];
  localVariables?: VariableData[];
  globalVariables?: VariableData[];
  functions?: FunctionData[];
}) => {
  if (data.currentVariables) currentVariables = data.currentVariables;
  if (data.localVariables) localVariables = data.localVariables;
  if (data.globalVariables) globalVariables = data.globalVariables;
  if (data.functions) functions = data.functions;
};

// 扁平化变量列表，生成所有可能的路径
const flattenVariables = (
  variables: VariableData[],
  prefix: string = '',
): Array<{ path: string; variable: VariableData }> => {
  const result: Array<{ path: string; variable: VariableData }> = [];

  variables.forEach((variable) => {
    const currentPath = prefix ? `${prefix}.${variable.key}` : variable.key;
    result.push({ path: currentPath, variable });

    if (variable.children && variable.children.length > 0) {
      result.push(...flattenVariables(variable.children, currentPath));
    }
  });

  return result;
};

// 获取变量建议
const getVariableSuggestions = (variables: VariableData[], prefix: string = ''): monaco.languages.CompletionItem[] => {
  const flattened = flattenVariables(variables);

  return flattened.map(({ path, variable }) => {
    const displayPath = prefix ? `${prefix}${path}` : path;
    return {
      label: displayPath,
      kind: monaco.languages.CompletionItemKind.Variable,
      insertText: displayPath,
      detail: variable.type,
      documentation: variable.description || variable.pathDescription || `${variable.type} 类型变量`,
      sortText: `0_${displayPath}`,
    };
  });
};

// 获取函数建议
const getFunctionSuggestions = (): monaco.languages.CompletionItem[] => {
  return functions.map((func) => ({
    label: func.label,
    kind: monaco.languages.CompletionItemKind.Function,
    insertText: func.script,
    detail: func.label,
    documentation: func.remark,
    sortText: `1_${func.label}`,
  }));
};

// 获取内置对象和方法的建议
const getBuiltinSuggestions = (): monaco.languages.CompletionItem[] => {
  return [
    {
      label: 'Utils',
      kind: monaco.languages.CompletionItemKind.Module,
      insertText: 'Utils.',
      detail: '工具类',
      documentation: '包含各种实用函数的工具类',
      sortText: '2_Utils',
    },
    {
      label: '_data',
      kind: monaco.languages.CompletionItemKind.Variable,
      insertText: '_data.',
      detail: '数据对象',
      documentation: '包含局部变量和全局变量的数据对象',
      sortText: '0__data',
    },
    {
      label: 'console.log',
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: 'console.log($1)',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '控制台输出',
      documentation: '在控制台输出信息',
      sortText: '3_console',
    },
    {
      label: 'JSON.parse',
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: 'JSON.parse($1)',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'JSON解析',
      documentation: '解析JSON字符串',
      sortText: '3_JSON',
    },
    {
      label: 'JSON.stringify',
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: 'JSON.stringify($1)',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'JSON序列化',
      documentation: '将对象序列化为JSON字符串',
      sortText: '3_JSON',
    },
  ];
};

// 注册TypeScript/JavaScript智能提示提供者
monaco.languages.registerCompletionItemProvider('typescript', {
  provideCompletionItems: (model, position) => {
    const suggestions: monaco.languages.CompletionItem[] = [];
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    // 简化的输入分析 - 检查是否以特定模式结尾
    const trimmedText = textBeforePointer.trim();

    // 如果是在 _data. 后面，提供局部和全局变量建议
    if (trimmedText.endsWith('_data.')) {
      // 添加局部变量建议
      const localSuggestions = getVariableSuggestions(localVariables, '_data.');
      suggestions.push(...localSuggestions);

      // 添加全局变量建议
      const globalSuggestions = getVariableSuggestions(globalVariables, '_data.');
      suggestions.push(...globalSuggestions);
    }
    // 如果是在 Utils. 后面，提供函数建议
    else if (trimmedText.endsWith('Utils.')) {
      const functionSuggestions = getFunctionSuggestions();
      suggestions.push(...functionSuggestions);
    }
    // 否则提供所有类型的建议
    else {
      // 添加临时变量建议（不需要 _data 前缀）
      const currentSuggestions = getVariableSuggestions(currentVariables);
      suggestions.push(...currentSuggestions);

      // 添加内置对象建议
      const builtinSuggestions = getBuiltinSuggestions();
      suggestions.push(...builtinSuggestions);

      // 添加函数建议
      const functionSuggestions = getFunctionSuggestions();
      suggestions.push(...functionSuggestions);
    }

    return {
      suggestions,
    };
  },
});

// 同样为JavaScript语言注册
monaco.languages.registerCompletionItemProvider('javascript', {
  provideCompletionItems: (model, position) => {
    // 复用TypeScript的逻辑
    const tsProviders = monaco.languages.getCompletionItemProviders('typescript');
    if (tsProviders.length > 0) {
      return tsProviders[0].provideCompletionItems(
        model,
        position,
        { triggerKind: 0, triggerCharacter: '' },
        { isCancellationRequested: false },
      );
    }
    return { suggestions: [] };
  },
});
